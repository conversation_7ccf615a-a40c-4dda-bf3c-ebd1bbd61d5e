package com.pacto.adm.core.dto.grupocolaborador;

import com.pacto.adm.core.dto.UsuarioDTO;
import com.pacto.adm.core.dto.colaborador.ColaboradorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Participante do Grupo Colaborador", description = "Informações do participante de um grupo de colaboradores")
public class GrupoColaboradorParticipanteDTO {

    @Schema(description = "Código único identificador do participante no grupo", example = "1")
    private Integer codigo;

    @Schema(description = "Tipo de visão ou permissão do participante no grupo. \n\n" +
            "**Valores disponíveis**\n" +
            "- VI (Visualização)\n" +
            "- IG (Integração)\n" +
            "- AC (Acesso)\n", example = "VI")
    private String tipoVisao;

    @Schema(description = "Detalhes do colaborador que participa do grupo")
    private ColaboradorDTO colaboradorParticipante;

    @Schema(description = "Detalhes do grupo de colaboradores ao qual o participante pertence")
    private GrupoColaboradorDTO grupoColaborador;

    @Schema(description = "Detalhes do usuário participante do grupo")
    private UsuarioDTO usuarioParticipante;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getTipoVisao() {
        return tipoVisao;
    }

    public void setTipoVisao(String tipoVisao) {
        this.tipoVisao = tipoVisao;
    }

    public ColaboradorDTO getColaboradorParticipante() {
        return colaboradorParticipante;
    }

    public void setColaboradorParticipante(ColaboradorDTO colaboradorParticipante) {
        this.colaboradorParticipante = colaboradorParticipante;
    }

    public GrupoColaboradorDTO getGrupoColaborador() {
        return grupoColaborador;
    }

    public void setGrupoColaborador(GrupoColaboradorDTO grupoColaborador) {
        this.grupoColaborador = grupoColaborador;
    }

    public UsuarioDTO getUsuarioParticipante() {
        return usuarioParticipante;
    }

    public void setUsuarioParticipante(UsuarioDTO usuarioParticipante) {
        this.usuarioParticipante = usuarioParticipante;
    }
}
