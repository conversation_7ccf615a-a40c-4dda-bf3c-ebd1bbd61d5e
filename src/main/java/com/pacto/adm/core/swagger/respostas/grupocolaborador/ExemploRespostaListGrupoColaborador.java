package com.pacto.adm.core.swagger.respostas.grupocolaborador;

import com.pacto.adm.core.dto.grupocolaborador.GrupoColaboradorDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Representação das respostas das requisições envolvendo grupos de colaboradores")
public class ExemploRespostaListGrupoColaborador {

    @Schema(description = "Conteúdo da resposta contendo as informações encontradas")
    private List<GrupoColaboradorDTO> content;

    public List<GrupoColaboradorDTO> getContent() {
        return content;
    }

    public void setContent(List<GrupoColaboradorDTO> content) {
        this.content = content;
    }

    public static final String resposta = "{"
            + "  \"content\": [{"
            + "    \"codigo\": 1,"
            + "    \"descricao\": \"Equipe de Vendas\","
            + "    \"empresa\": {"
            + "      \"codigo\": 1,"
            + "      \"nome\": \"ACADEMIA PACTO\","
            + "      \"ativa\": true,"
            + "      \"setor\": \"Academia\""
            + "    },"
            + "    \"gerente\": {"
            + "      \"codigo\": 1,"
            + "      \"nome\": \"João Silva\""
            + "    },"
            + "    \"grupoColaboradorParticipantes\": [{"
            + "      \"codigo\": 1,"
            + "      \"tipoVisao\": \"VI\","
            + "      \"colaboradorParticipante\": {"
            + "        \"codigo\": 1,"
            + "        \"situacao\": \"AT\","
            + "        \"pessoa\": {"
            + "          \"codigo\": 12345,"
            + "          \"nome\": \"Maria Santos\","
            + "          \"cpf\": \"123.456.789-00\""
            + "        }"
            + "      },"
            + "      \"usuarioParticipante\": {"
            + "        \"codigo\": 1,"
            + "        \"nome\": \"Maria Santos\""
            + "      }"
            + "    }, {"
            + "      \"codigo\": 2,"
            + "      \"tipoVisao\": \"AC\","
            + "      \"colaboradorParticipante\": {"
            + "        \"codigo\": 2,"
            + "        \"situacao\": \"AT\","
            + "        \"pessoa\": {"
            + "          \"codigo\": 12346,"
            + "          \"nome\": \"Carlos Oliveira\","
            + "          \"cpf\": \"987.654.321-00\""
            + "        }"
            + "      },"
            + "      \"usuarioParticipante\": {"
            + "        \"codigo\": 2,"
            + "        \"nome\": \"Carlos Oliveira\""
            + "      }"
            + "    }],"
            + "    \"situacaoGrupo\": \"AT\","
            + "    \"tipoGrupo\": \"GR\","
            + "    \"semGrupo\": false"
            + "  }, {"
            + "    \"codigo\": 2,"
            + "    \"descricao\": \"Equipe de Atendimento\","
            + "    \"empresa\": {"
            + "      \"codigo\": 1,"
            + "      \"nome\": \"ACADEMIA PACTO\","
            + "      \"ativa\": true,"
            + "      \"setor\": \"Academia\""
            + "    },"
            + "    \"gerente\": {"
            + "      \"codigo\": 3,"
            + "      \"nome\": \"Ana Costa\""
            + "    },"
            + "    \"grupoColaboradorParticipantes\": [{"
            + "      \"codigo\": 3,"
            + "      \"tipoVisao\": \"IG\","
            + "      \"colaboradorParticipante\": {"
            + "        \"codigo\": 3,"
            + "        \"situacao\": \"AT\","
            + "        \"pessoa\": {"
            + "          \"codigo\": 12347,"
            + "          \"nome\": \"Pedro Almeida\","
            + "          \"cpf\": \"456.789.123-00\""
            + "        }"
            + "      },"
            + "      \"usuarioParticipante\": {"
            + "        \"codigo\": 3,"
            + "        \"nome\": \"Pedro Almeida\""
            + "      }"
            + "    }],"
            + "    \"situacaoGrupo\": \"AT\","
            + "    \"tipoGrupo\": \"FA\","
            + "    \"semGrupo\": false"
            + "  }, {"
            + "    \"codigo\": null,"
            + "    \"descricao\": \"SEM GRUPO\","
            + "    \"empresa\": null,"
            + "    \"gerente\": null,"
            + "    \"grupoColaboradorParticipantes\": [{"
            + "      \"codigo\": 4,"
            + "      \"tipoVisao\": null,"
            + "      \"colaboradorParticipante\": {"
            + "        \"codigo\": 4,"
            + "        \"situacao\": \"AT\","
            + "        \"pessoa\": {"
            + "          \"codigo\": 12348,"
            + "          \"nome\": \"Lucas Ferreira\","
            + "          \"cpf\": \"789.123.456-00\""
            + "        }"
            + "      },"
            + "      \"usuarioParticipante\": {"
            + "        \"codigo\": 4,"
            + "        \"nome\": \"Lucas Ferreira\""
            + "      }"
            + "    }],"
            + "    \"situacaoGrupo\": null,"
            + "    \"tipoGrupo\": null,"
            + "    \"semGrupo\": true"
            + "  }]"
            + "}";
}
