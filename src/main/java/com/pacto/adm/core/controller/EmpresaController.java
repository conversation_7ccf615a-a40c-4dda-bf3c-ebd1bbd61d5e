package com.pacto.adm.core.controller;


import com.pacto.adm.core.dto.empresa.EmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaEmpresaDTO;
import com.pacto.adm.core.dto.enveloperesposta.empresa.EnvelopeRespostaListEmpresaDTO;
import com.pacto.adm.core.dto.filtros.FiltroEmpresaJSON;
import com.pacto.adm.core.services.interfaces.EmpresaService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/empresas")
@Tag(name = "Empresa (ADM)")
public class EmpresaController {
    private final EmpresaService empresaService;

    public EmpresaController(EmpresaService empresaService) {
        this.empresaService = empresaService;
    }

    @Operation(
            summary = "Consultar empresas cadastradas",
            description = "Consulta as empresas cadastradas podendo aplicar filtros para buscar por empresa específica pelo código e/ou se está ativa.",
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtro de busca para o objeto.<br/>" +
                                    "<br/><strong>Filtros disponíveis</strong>" +
                                    "<ul>" +
                                    "<li><strong>codigo:</strong> Código único da empresa para busca específica.</li>" +
                                    "<li><strong>ativa:</strong> Busca apenas por empresas ativas no sistema (true ou false).</li>" +
                                    "</ul>",
                            example = "{\"codigo\":123,\"ativa\":true}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página", example = "0", schema = @Schema(implementation = String.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaListEmpresaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaListEmpresaDTO.resposta)
                            )
                    )}
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros) {
        try {
            FiltroEmpresaJSON filtroEmpresaJSON = new FiltroEmpresaJSON(filtros);
            return ResponseEntityFactory.ok(empresaService.findAll(filtroEmpresaJSON));
        } catch (ServiceException exception) {
            StringBuilder result = new StringBuilder(exception.toString() + "\n");
            StackTraceElement[] trace = exception.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(exception.getChaveExcecao(), result.toString());
        }
    }

    @Operation(
            summary = "Consultar uma empresa",
            description = "Consulta as informações de uma empresa buscando pelo código identificador dela.",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da empresa que será consultada", example = "122", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaEmpresaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaEmpresaDTO.resposta)
                            )
                    )}
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable(required = false) Integer id) {
        try {
            return ResponseEntityFactory.ok(empresaService.findById(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Operation(
            summary = "Consultar o nome de uma empresa",
            description = "Consulta apenas o nome de uma empresa pelo código dela. No exemplo de resposta há outras informações, mas essa consulta apenas retorna o CODIGO e NOME da empresa!",
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da empresa que será consultada", example = "122", required = true)
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaEmpresaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaEmpresaDTO.resposta)
                            )
                    )}
    )
    @GetMapping(value = "/{id}/only-cod-name")
    public ResponseEntity<EnvelopeRespostaDTO> findEmpresaByIdOnlyCodName(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(empresaService.findByIdOnlyCodName(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Operation(
            summary = "Criar ou atualizar uma empresa",
            description = "Cria ou atualiza as informações de uma empresa",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Exemplo das informações para criar ou atualizar.<br/>" +
                            "Se for criar um novo registro, envie a requisição sem o atributo código.<br/>" +
                            "Se for atualizar um registro, envie a requisição com o atribbuto código contendo o valor do registro que será atualizado.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = EmpresaDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaEmpresaDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(responseCode = "200", description = "Solicitação bem-sucedida.",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnvelopeRespostaEmpresaDTO.class),
                                    examples = @ExampleObject(name = "Status 200", summary = "Exemplo de resposta com o status 200", value = EnvelopeRespostaEmpresaDTO.resposta)
                            )
                    )}
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@RequestBody EmpresaDTO empresaDTO) {
        try {
            return ResponseEntityFactory.ok(empresaService.saveOrUpdate(empresaDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }
}
