package com.pacto.adm.core.controller;

import com.pacto.adm.core.services.interfaces.GrupoColaboradorService;
import com.pacto.adm.core.swagger.respostas.grupocolaborador.ExemploRespostaListGrupoColaborador;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/grupo-colaborador")
public class GrupoColaboradorController {

    private final GrupoColaboradorService grupoColaboradorService;

    public GrupoColaboradorController(GrupoColaboradorService grupoColaboradorService) {
        this.grupoColaboradorService = grupoColaboradorService;
    }

    @GetMapping("/by-empresa-logada")
    public ResponseEntity<EnvelopeRespostaDTO> findByEmpresaLogada() {
        try {
            return ResponseEntityFactory.ok(grupoColaboradorService.montarGruposColaboradores());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("Ocorreu um erro desconhecido ", e.getMessage());
        }
    }
}
